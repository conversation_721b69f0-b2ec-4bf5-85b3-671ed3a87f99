import { NextResponse } from 'next/server';
import { QdrantClient } from '@qdrant/js-client-rest';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { APP_CONFIG } from '@/lib/config';

export async function POST() {
  try {
    console.log('🔄 Testing RAG chat functionality...');

    // Check if required services are configured
    if (!process.env.QDRANT_URL) {
      return NextResponse.json(
        { error: 'Qdrant is not configured' },
        { status: 503 }
      );
    }

    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        { error: 'Gemini API key is not configured' },
        { status: 503 }
      );
    }

    // Initialize clients
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: APP_CONFIG.api.chat.model });

    // Test data - create some Q&A pairs first
    const testCollectionName = 'test_rag_collection_' + Date.now();
    const testUserId = 'test-user-rag';
    const testCollectionId = 'test-collection-rag';

    console.log('📊 Setting up test data...');

    // Create test Q&A pairs
    const testQAPairs = [
      {
        question: "What is the capital of France?",
        answer: "The capital of France is Paris, a beautiful city known for the Eiffel Tower."
      },
      {
        question: "What is the largest planet in our solar system?",
        answer: "Jupiter is the largest planet in our solar system, with a mass greater than all other planets combined."
      },
      {
        question: "Who wrote Romeo and Juliet?",
        answer: "William Shakespeare wrote Romeo and Juliet, one of his most famous tragedies."
      }
    ];

    try {
      // Create collection
      await qdrantClient.createCollection(testCollectionName, {
        vectors: {
          size: 768, // Gemini text-embedding-004 dimensions
          distance: 'Cosine',
        },
      });
      console.log('✅ Test collection created');

      // Add test Q&A pairs to Qdrant
      for (let i = 0; i < testQAPairs.length; i++) {
        const qa = testQAPairs[i];
        const testVector = Array.from({ length: 384 }, () => Math.random() * 2 - 1);
        
        const crypto = require('crypto');
        const hash = crypto.createHash('md5').update(`${testCollectionId}_${qa.question}`).digest('hex');
        const pointId = `${hash.substring(0, 8)}-${hash.substring(8, 12)}-${hash.substring(12, 16)}-${hash.substring(16, 20)}-${hash.substring(20, 32)}`;

        await qdrantClient.upsert(testCollectionName, {
          wait: true,
          points: [
            {
              id: pointId,
              vector: testVector,
              payload: {
                question: qa.question,
                answer: qa.answer,
                collection_id: testCollectionId,
                user_id: testUserId,
                created_at: new Date().toISOString(),
              },
            },
          ],
        });
      }
      console.log('✅ Test Q&A pairs added to Qdrant');

      // Test RAG search
      const queryQuestion = "What is the capital of France?";
      const queryVector = Array.from({ length: 768 }, () => Math.random() * 2 - 1);

      console.log('📊 Testing vector search...');
      const searchResults = await qdrantClient.search(testCollectionName, {
        vector: queryVector,
        limit: 3,
        score_threshold: 0.0, // Low threshold for testing
        with_payload: true,
      });

      console.log('✅ Search completed, found', searchResults.length, 'results');

      // Test RAG response generation
      if (searchResults.length > 0) {
        console.log('📊 Testing RAG response generation...');
        
        const context = searchResults.map(result => 
          `Q: ${result.payload?.question}\nA: ${result.payload?.answer}`
        ).join('\n\n');

        const prompt = `Based on the following context, answer the question: "${queryQuestion}"

Context:
${context}

Please provide a helpful answer based on the context above. If the context doesn't contain relevant information, say so.`;

        const result = await model.generateContent(prompt);
        const response = result.response;
        const answer = response.text();

        console.log('✅ RAG response generated');

        // Clean up
        await qdrantClient.deleteCollection(testCollectionName);
        console.log('✅ Test collection cleaned up');

        return NextResponse.json({
          success: true,
          message: 'RAG chat functionality test completed successfully',
          results: {
            collectionName: testCollectionName,
            testQAPairs: testQAPairs.length,
            searchResults: searchResults.length,
            queryQuestion: queryQuestion,
            ragAnswer: answer,
            workflow: [
              'Test collection created',
              'Q&A pairs added to Qdrant',
              'Vector search performed',
              'RAG response generated',
              'Test data cleaned up'
            ]
          },
        });

      } else {
        // Clean up even if no search results
        await qdrantClient.deleteCollection(testCollectionName);
        
        return NextResponse.json({
          success: false,
          message: 'No search results found - vector search may need tuning',
          results: {
            collectionName: testCollectionName,
            testQAPairs: testQAPairs.length,
            searchResults: 0,
          },
        });
      }

    } catch (error) {
      console.error('❌ RAG test failed:', error);
      
      // Try to clean up
      try {
        await qdrantClient.deleteCollection(testCollectionName);
        console.log('✅ Test collection cleaned up after error');
      } catch (cleanupError) {
        console.warn('⚠️ Failed to clean up test collection:', cleanupError);
      }

      return NextResponse.json(
        { 
          error: 'RAG test failed', 
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ Test setup failed:', error);
    return NextResponse.json(
      { 
        error: 'Test setup failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
