'use client';

import { APP_CONFIG } from '@/lib/config';

// Primary embedding function using Gemini
export async function generateQAEmbedding(question: string, answer: string = ''): Promise<number[]> {
  const embeddingConfig = APP_CONFIG.api.embedding;
  console.log(`🌐 Generating ${embeddingConfig.provider} embedding...`);

  try {
    return await generatePrimaryEmbedding(question, answer);
  } catch (error) {
    console.error('❌ Embedding generation failed:', error);
    throw new Error('Failed to generate embedding: All methods failed. Please check your internet connection and try again.');
  }
}

// Specialized function for search queries (only question, no answer)
export async function generateSearchEmbedding(query: string): Promise<number[]> {
  const embeddingConfig = APP_CONFIG.api.embedding;
  console.log(`🔍 Generating ${embeddingConfig.provider} search embedding...`);

  try {
    return await generatePrimarySearchEmbedding(query);
  } catch (error) {
    console.error('❌ Search embedding generation failed:', error);
    throw new Error('Failed to generate search embedding: All methods failed. Please check your internet connection and try again.');
  }
}

/**
 * Sync Q&A pair with Qdrant
 */
export async function syncToQdrant(data: {
  question: string;
  answer: string;
  vector: number[];
  collection_id: string;
  user_id: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🔄 Syncing to Qdrant:', {
      collection_id: data.collection_id,
      user_id: data.user_id,
      vectorDimensions: data.vector.length,
    });

    const response = await fetch('/api/qdrant-sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'Failed to sync with Qdrant');
    }

    console.log('✅ Successfully synced to Qdrant:', result);
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to sync to Qdrant:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete Q&A pair from Qdrant
 */
export async function deleteFromQdrant(data: {
  question: string;
  collection_id: string;
  user_id: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🗑️ Deleting from Qdrant:', {
      collection_id: data.collection_id,
      user_id: data.user_id,
      question: data.question.substring(0, 50) + '...',
    });

    const response = await fetch('/api/qdrant-delete', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      console.warn('⚠️ Qdrant deletion failed:', result.error);
      return {
        success: false,
        error: result.error || 'Failed to delete from Qdrant'
      };
    }

    console.log('✅ Successfully deleted from Qdrant:', result);
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to delete from Qdrant:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}



// Primary embedding functions using centralized config
async function generatePrimaryEmbedding(question: string, answer: string = ''): Promise<number[]> {
  try {
    const embeddingConfig = APP_CONFIG.api.embedding;
    console.log(`🌐 Generating ${embeddingConfig.provider} Q&A embedding...`);

    const response = await fetch('/api/generate-gemini-embedding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        question,
        answer,
        type: 'qa_pair'
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();

      // Check if it's a rate limit or API issue that should trigger fallback
      if (response.status === 429 || response.status === 503 || response.status >= 500) {
        throw new Error(`${embeddingConfig.provider} API temporarily unavailable: ${errorData.error}`);
      } else {
        throw new Error(errorData.error || `${embeddingConfig.provider} API error: ${response.status}`);
      }
    }

    const result = await response.json();

    if (!result.success || !result.embedding) {
      throw new Error(result.error || `${embeddingConfig.provider} embedding generation failed`);
    }

    console.log(`✅ ${embeddingConfig.provider} Q&A embedding generated:`, {
      dimensions: result.embedding.length,
      model: result.model,
      response_time_ms: result.response_time_ms,
      tokens_used: result.tokens_used
    });

    return result.embedding;
  } catch (error) {
    console.error(`❌ ${APP_CONFIG.api.embedding.provider} Q&A embedding generation failed:`, error);
    throw error;
  }
}

async function generatePrimarySearchEmbedding(query: string): Promise<number[]> {
  try {
    const embeddingConfig = APP_CONFIG.api.embedding;
    console.log(`🌐 Generating ${embeddingConfig.provider} search embedding...`);

    const response = await fetch('/api/generate-gemini-embedding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        type: 'search_query'
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();

      // Check if it's a rate limit or API issue that should trigger fallback
      if (response.status === 429 || response.status === 503 || response.status >= 500) {
        throw new Error(`${embeddingConfig.provider} API temporarily unavailable: ${errorData.error}`);
      } else {
        throw new Error(errorData.error || `${embeddingConfig.provider} API error: ${response.status}`);
      }
    }

    const result = await response.json();

    if (!result.success || !result.embedding) {
      throw new Error(result.error || `${embeddingConfig.provider} search embedding generation failed`);
    }

    console.log(`✅ ${embeddingConfig.provider} search embedding generated:`, {
      dimensions: result.embedding.length,
      model: result.model,
      response_time_ms: result.response_time_ms,
      tokens_used: result.tokens_used
    });

    return result.embedding;
  } catch (error) {
    console.error(`❌ ${APP_CONFIG.api.embedding.provider} search embedding generation failed:`, error);
    throw error;
  }
}


